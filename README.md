# Solana Tracker

Real-time Solana transaction tracking with Telegram notifications.

## Features

- Track wallet transactions via webhooks
- Telegram notifications with Solscan links
- Token metadata enrichment
- Wallet management

## Setup

```bash
# Install
pnpm install

# Configure environment
cp .env.example .env
# Add your TELEGRAM_BOT_TOKEN, MONGODB_URI, API_TOKEN, and HELIUS_WEBHOOK_TOKEN

# Run
pnpm run start:dev
```

## API

### Webhook Endpoints (Require Helius Token)

- `POST /webhook/helius` - Process webhook events (Helius webhooks)

### Protected Endpoints (Require API Token)

- `GET /wallet` - List all tracked wallets
- `GET /wallet/active` - List active tracked wallets
- `GET /wallet/:address` - Get specific wallet
- `POST /wallet/add` - Add wallet for tracking

### Authentication

**Global Authentication**: All API endpoints require Bearer token authentication by default. Only endpoints explicitly marked as `@Public()` bypass authentication.

Include the API token in the Authorization header:

```bash
# Using Authorization header with Bearer token
curl -H "Authorization: Bearer your-api-token" http://localhost:3000/wallet

# Example with POST request
curl -X POST -H "Authorization: Bearer your-api-token" \
  -H "Content-Type: application/json" \
  -d '{"address":"wallet-address-here"}' \
  http://localhost:3000/wallet/add
```

**Configuration**: Set `API_TOKEN=your-secure-token` in your `.env` file.

## Bot Commands

- `/add <wallet>` - Track wallet
- `/list` - Show tracked wallets
- `/remove <wallet>` - Stop tracking

## Tech Stack

- NestJS, TypeScript, MongoDB
- Telegram Bot API, Solana Web3.js
