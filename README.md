# Solana Tracker

Real-time Solana transaction tracking with Telegram notifications.

## Features

- Track wallet transactions via webhooks
- Telegram notifications with Solscan links
- Token metadata enrichment
- Wallet management

## Setup

```bash
# Install
pnpm install

# Configure environment
cp .env.example .env

# Run
pnpm run start:dev
```

## API

### Public Endpoints

- `POST /webhook/helius` - Process webhook events (Helius webhooks)

### Protected Endpoints (Require API Token)

**All endpoints are protected by default except those marked as public above.**

- `GET /wallet` - List all tracked wallets
- `GET /wallet/active` - List active tracked wallets
- `GET /wallet/:address` - Get specific wallet
- `POST /wallet/add` - Add wallet for tracking

### Authentication

**Global Authentication**: All API endpoints require authentication by default. Only endpoints explicitly marked as `@Public()` bypass authentication.

Include the API token using Bearer authentication:

```bash
# Using Authorization header with Bearer token
curl -H "Authorization: Bearer your-api-token" http://localhost:3000/wallet
```

## Bot Commands

- `/add <wallet>` - Track wallet
- `/list` - Show tracked wallets
- `/remove <wallet>` - Stop tracking

## Tech Stack

- NestJS, TypeScript, MongoDB
- Telegram Bot API, Solana Web3.js
