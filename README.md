# Solana Tracker

Real-time Solana transaction tracking with Telegram notifications.

## Features

- Track wallet transactions via webhooks
- Telegram notifications with Solscan links
- Token metadata enrichment
- Wallet management

## Setup

```bash
# Install
pnpm install

# Configure environment
cp .env.example .env
# Add your TELEGRAM_BOT_TOKEN and MONGODB_URI

# Run
pnpm run start:dev
```

## API

- `POST /webhook/helius` - Process webhook events
- `GET /wallets` - List tracked wallets  
- `POST /wallets` - Add wallet for tracking

## Bot Commands

- `/add <wallet>` - Track wallet
- `/list` - Show tracked wallets
- `/remove <wallet>` - Stop tracking

## Tech Stack

- NestJS, TypeScript, MongoDB
- Telegram Bot API, Solana Web3.js
