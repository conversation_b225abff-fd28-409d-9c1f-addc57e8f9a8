# Solana Tracker

Real-time Solana transaction tracking with Telegram notifications.

## Features

- Track wallet transactions via webhooks
- Telegram notifications with Solscan links
- Token metadata enrichment
- Wallet management

## Setup

```bash
# Install
pnpm install

# Configure environment
cp .env.example .env
# Add your TELEGRAM_BOT_TOKEN, MONGODB_URI, and API_TOKEN

# Run
pnpm run start:dev
```

## API

### Public Endpoints

- `POST /webhook/helius` - Process webhook events (Helius webhooks)
- `GET /health` - Health check

### Protected Endpoints (Require API Token)

- `GET /health/protected` - Protected health check
- `GET /wallet` - List all tracked wallets
- `GET /wallet/active` - List active tracked wallets
- `GET /wallet/:address` - Get specific wallet
- `POST /wallet/add` - Add wallet for tracking

### Authentication

Protected endpoints require an API token. Include it in one of these ways:

```bash
# Using Authorization header with Bearer
curl -H "Authorization: Bearer your-api-token" http://localhost:3000/wallet

# Using Authorization header directly
curl -H "Authorization: your-api-token" http://localhost:3000/wallet

# Using X-API-Token header
curl -H "X-API-Token: your-api-token" http://localhost:3000/wallet
```

## Bot Commands

- `/add <wallet>` - Track wallet
- `/list` - Show tracked wallets
- `/remove <wallet>` - Stop tracking

## Tech Stack

- NestJS, TypeScript, MongoDB
- Telegram Bot API, Solana Web3.js
