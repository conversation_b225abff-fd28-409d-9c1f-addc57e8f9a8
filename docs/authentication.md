# API Authentication

## Overview

The Solana Tracker API uses global authentication with Bearer tokens. All endpoints are protected by default unless explicitly marked as public.

## Authentication

Include your API token in the Authorization header:

```bash
Authorization: Bearer your-api-token
```

## Configuration

Set your API token in `.env`:

```bash
API_TOKEN=your-secure-api-token-here
```

## Adding New Endpoints

### Protected Endpoint (Default)

```typescript
@Controller('example')
export class ExampleController {
  @Get()
  getProtectedData() {
    // This endpoint requires authentication automatically
    return { data: 'protected' };
  }
}
```

### Public Endpoint

```typescript
@Controller('example')
export class ExampleController {
  @Get('public')
  @Public() // Mark as public to bypass authentication
  getPublicData() {
    return { data: 'public' };
  }
}
```

## Examples

### Testing with curl

```bash
# Public endpoint (no auth needed)
curl -X POST -H "Content-Type: application/json" \
  -d '{"feePayer":"test","tokenTransfers":[],"accountData":[]}' \
  http://localhost:3000/webhook/helius

# Protected endpoint (auth required)
curl -H "Authorization: Bearer your-token" http://localhost:3000/wallet

# Another protected endpoint
curl -H "Authorization: Bearer your-token" http://localhost:3000/wallet/active
```

### Client Implementation

```javascript
// JavaScript/Node.js example
const apiToken = 'your-api-token';

// Using fetch with Bearer token
const response = await fetch('http://localhost:3000/wallet', {
  headers: {
    Authorization: `Bearer ${apiToken}`,
    'Content-Type': 'application/json',
  },
});

// Using axios with Bearer token
const response = await axios.get('http://localhost:3000/wallet', {
  headers: {
    Authorization: `Bearer ${apiToken}`,
  },
});
```

## Troubleshooting

### Common Issues

**401 Unauthorized - "Bearer token required"**

- Missing Authorization header
- Check that you're using the Bearer token format: `Authorization: Bearer your-token`

**401 Unauthorized - "Invalid token"**

- Wrong token value
- Verify the token matches the `API_TOKEN` environment variable

### Changing the Token

1. Update the `API_TOKEN` value in your `.env` file
2. Restart the application
3. Update client applications with the new token
