# API Authentication Guide

## Overview

The Solana Tracker API uses **global authentication** with a fixed API token. All endpoints are protected by default unless explicitly marked as public.

## How It Works

### Global Guard

- The `ApiTokenGuard` is applied globally to all routes via `APP_GUARD` in `AppModule`
- Every request is intercepted and checked for authentication
- Public endpoints are exempted using the `@Public()` decorator

### Authentication Flow

1. **Request arrives** → Guard checks for `@Public()` metadata
2. **If public** → Skip authentication, allow access
3. **If protected** → Extract token from headers
4. **Validate token** → Compare with `API_TOKEN` environment variable
5. **Grant/Deny access** → Return 401 if invalid, continue if valid

## Adding New Endpoints

### Protected Endpoint (Default)

```typescript
@Controller('example')
export class ExampleController {
  @Get()
  getProtectedData() {
    // This endpoint requires authentication automatically
    return { data: 'protected' };
  }
}
```

### Public Endpoint

```typescript
@Controller('example')
export class ExampleController {
  @Get('public')
  @Public() // Mark as public to bypass authentication
  getPublicData() {
    return { data: 'public' };
  }
}
```

## Authentication Headers

Clients must authenticate using Bearer token format:

```bash
# Bearer token format (only supported format)
Authorization: Bearer your-api-token
```

## Configuration

### Environment Variables

```bash
# Required: API token for authentication
API_TOKEN=your-secure-api-token-here
```

### Changing the Token

1. Update the `API_TOKEN` value in your `.env` file
2. Restart the application
3. Update client applications with the new token

## Security Considerations

### Current Implementation

- ✅ Bearer token authentication
- ✅ Global protection by default
- ✅ Comprehensive logging
- ✅ Simple and secure token format

### Future Improvements

- [ ] JWT tokens with expiration
- [ ] Role-based access control
- [ ] Rate limiting per token
- [ ] Token rotation mechanism
- [ ] Webhook signature validation

## Troubleshooting

### Common Issues

**401 Unauthorized - "API token is required"**

- Missing Authorization header
- Check that you're using the Bearer token format: `Authorization: Bearer your-token`

**401 Unauthorized - "Invalid API token"**

- Wrong token value
- Verify the token matches the `API_TOKEN` environment variable

**401 Unauthorized - "API authentication not configured"**

- `API_TOKEN` environment variable is not set
- Check your `.env` file and restart the application

### Debug Logging

Enable debug logging to see authentication activity:

```bash
# The guard logs authentication attempts:
# DEBUG: "Public endpoint accessed - skipping authentication"
# DEBUG: "API request authenticated successfully"
# WARN:  "API request without token"
# WARN:  "API request with invalid token"
```

## Examples

### Testing with curl

```bash
# Public endpoint (no auth needed)
curl -X POST -H "Content-Type: application/json" \
  -d '{"feePayer":"test","tokenTransfers":[],"accountData":[]}' \
  http://localhost:3000/webhook/helius

# Protected endpoint (auth required)
curl -H "Authorization: Bearer your-token" http://localhost:3000/wallet

# Another protected endpoint
curl -H "Authorization: Bearer your-token" http://localhost:3000/wallet/active
```

### Client Implementation

```javascript
// JavaScript/Node.js example
const apiToken = 'your-api-token';

// Using fetch with Bearer token
const response = await fetch('http://localhost:3000/wallet', {
  headers: {
    Authorization: `Bearer ${apiToken}`,
    'Content-Type': 'application/json',
  },
});

// Using axios with Bearer token
const response = await axios.get('http://localhost:3000/wallet', {
  headers: {
    Authorization: `Bearer ${apiToken}`,
  },
});
```
