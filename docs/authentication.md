# API Authentication Guide

## Overview

The Solana Tracker API uses **global authentication** with a fixed API token. All endpoints are protected by default unless explicitly marked as public.

## How It Works

### Global Guard

- The `ApiTokenGuard` is applied globally to all routes via `APP_GUARD` in `AppModule`
- Every request is intercepted and checked for authentication
- Public endpoints are exempted using the `@Public()` decorator

### Authentication Flow

1. **Request arrives** → Guard checks for `@Public()` metadata
2. **If public** → Skip authentication, allow access
3. **If protected** → Extract token from headers
4. **Validate token** → Compare with `API_TOKEN` environment variable
5. **Grant/Deny access** → Return 401 if invalid, continue if valid

## Adding New Endpoints

### Protected Endpoint (Default)

```typescript
@Controller('example')
export class ExampleController {
  @Get()
  getProtectedData() {
    // This endpoint requires authentication automatically
    return { data: 'protected' };
  }
}
```

### Public Endpoint

```typescript
@Controller('example')
export class ExampleController {
  @Get('public')
  @Public() // Mark as public to bypass authentication
  getPublicData() {
    return { data: 'public' };
  }
}
```

## Authentication Headers

Clients can authenticate using any of these header formats:

```bash
# Bearer token format
Authorization: Bearer your-api-token

# Direct token format
Authorization: your-api-token

# Custom header format
X-API-Token: your-api-token
```

## Configuration

### Environment Variables

```bash
# Required: API token for authentication
API_TOKEN=your-secure-api-token-here
```

### Changing the Token

1. Update the `API_TOKEN` value in your `.env` file
2. Restart the application
3. Update client applications with the new token
