# API Authentication

## Setup

Add tokens to `.env`:
```bash
API_TOKEN=your-api-token
HELIUS_WEBHOOK_TOKEN=your-webhook-token
```

## Usage

### API Endpoints
```bash
curl -H "Authorization: Bearer your-api-token" http://localhost:3000/wallet
```

### Webhook Endpoints
```bash
curl -H "Authorization: Bearer your-webhook-token" http://localhost:3000/webhook/helius
```

## Adding Endpoints

```typescript
// Protected (default)
@Get()
getData() { return data; }

// Helius webhook
@Post('webhook')
@HeliusAuth()
webhook() { return ok; }
```
