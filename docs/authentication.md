# API Authentication

## Overview

All API endpoints require Bearer token authentication except those marked as `@Public()`.

## Authentication

Include your API token in the Authorization header:

```bash
Authorization: Bearer your-api-token
```

## Configuration

Set your API token in `.env`:

```bash
API_TOKEN=your-secure-api-token-here
```

## Adding New Endpoints

```typescript
// Protected endpoint (default)
@Get()
getProtectedData() {
  return { data: 'protected' };
}

// Public endpoint
@Get('public')
@Public()
getPublicData() {
  return { data: 'public' };
}
```

## Examples

```bash
# Protected endpoint
curl -H "Authorization: Bearer your-token" http://localhost:3000/wallet

# Public endpoint (no auth needed)
curl -X POST http://localhost:3000/webhook/helius
```
