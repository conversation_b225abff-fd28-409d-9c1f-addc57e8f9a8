{"name": "@solana-tracker/api", "version": "0.0.1", "description": "Solana Tracker API", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "clean": "rimraf dist coverage"}, "dependencies": {"@metaplex-foundation/digital-asset-standard-api": "^2.0.0", "@metaplex-foundation/umi": "^1.2.0", "@metaplex-foundation/umi-bundle-defaults": "^1.2.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/mongoose": "^11.0.3", "@nestjs/platform-express": "^11.0.1", "@solana/web3.js": "^1.98.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "helius-sdk": "^1.5.1", "mongoose": "^8.15.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "telegraf": "^4.16.3", "zod": "^3.25.42"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/supertest": "^6.0.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-loader": "^9.5.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}