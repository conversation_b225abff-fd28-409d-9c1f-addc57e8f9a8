import { Controller, Get, Logger } from '@nestjs/common';
import { ApiAuth } from '../../common/decorators/api-auth.decorator';

@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  @Get()
  getHealth() {
    this.logger.log('Health check - public endpoint');
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'solana-tracker-api',
    };
  }

  @Get('protected')
  @ApiAuth()
  getProtectedHealth() {
    this.logger.log('Protected health check - authenticated endpoint');
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'solana-tracker-api',
      authenticated: true,
    };
  }
}
