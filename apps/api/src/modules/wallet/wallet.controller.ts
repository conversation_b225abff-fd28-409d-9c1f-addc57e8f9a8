import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  HttpCode,
  HttpStatus,
  Logger,
  UseFilters,
} from '@nestjs/common';
import { TrackedWallet } from './wallet.schema';
import { AddWalletDto } from './wallet.dto';
import { WalletService } from './wallet.service';
import { WalletExceptionFilter } from './wallet.exception-filter';

// TODO: Add pagination for wallet listing endpoints
// TODO: Implement wallet search and filtering capabilities
// TODO: Add wallet analytics and statistics endpoints
// TODO: Consider adding bulk wallet operations (add/remove multiple)

@Controller('wallet')
@UseFilters(WalletExceptionFilter)
export class WalletController {
  private readonly logger = new Logger(WalletController.name);

  constructor(private readonly walletService: WalletService) {}

  @Post('add')
  @HttpCode(HttpStatus.CREATED)
  // TODO: Add wallet address validation and format checking
  // TODO: Implement duplicate wallet detection and handling
  addWallet(@Body() addWalletDto: AddWalletDto): Promise<TrackedWallet> {
    this.logger.log(`Adding wallet: ${addWalletDto.address}`);
    return this.walletService.addWallet(addWalletDto.address);
  }

  @Get()
  // TODO: Add pagination query parameters (page, limit, offset)
  // TODO: Add response caching for frequently accessed wallet lists
  getAllWallets(): Promise<TrackedWallet[]> {
    this.logger.log('Fetching all wallets');
    return this.walletService.getAllWallets();
  }

  @Get('active')
  getActiveWallets(): Promise<TrackedWallet[]> {
    this.logger.log('Fetching active wallets');
    return this.walletService.getActiveWallets();
  }

  @Get(':address')
  getWallet(@Param('address') address: string): Promise<TrackedWallet> {
    this.logger.log(`Fetching wallet: ${address}`);
    return this.walletService.getWallet(address);
  }
}
