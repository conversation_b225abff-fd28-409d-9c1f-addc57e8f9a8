import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

@Injectable()
export class HeliusWebhookGuard implements CanActivate {
  constructor(private readonly configService: ConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const authHeader = request.headers.authorization;
    
    if (!authHeader?.startsWith('Bearer ')) {
      throw new UnauthorizedException('Helius webhook token required');
    }

    const token = authHeader.substring(7);
    const validToken = this.configService.get<string>('HELIUS_WEBHOOK_TOKEN');
    
    if (token !== validToken) {
      throw new UnauthorizedException('Invalid webhook token');
    }

    return true;
  }
}
