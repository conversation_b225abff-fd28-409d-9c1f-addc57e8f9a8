import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

@Injectable()
export class ApiTokenGuard implements CanActivate {
  private readonly logger = new Logger(ApiTokenGuard.name);

  constructor(private readonly configService: ConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      this.logger.warn('API request without token');
      throw new UnauthorizedException('API token is required');
    }

    const validToken = this.configService.get<string>('API_TOKEN');
    if (!validToken) {
      this.logger.error('API_TOKEN is not configured');
      throw new UnauthorizedException('API authentication not configured');
    }

    if (token !== validToken) {
      this.logger.warn('API request with invalid token');
      throw new UnauthorizedException('Invalid API token');
    }

    this.logger.debug('API request authenticated successfully');
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    // Support both Authorization header formats:
    // - Authorization: Bearer <token>
    // - Authorization: <token>
    const authHeader = request.headers.authorization;
    if (authHeader) {
      if (authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7);
      }
      return authHeader;
    }

    // Also support X-API-Token header
    const apiTokenHeader = request.headers['x-api-token'];
    if (typeof apiTokenHeader === 'string') {
      return apiTokenHeader;
    }

    return undefined;
  }
}
