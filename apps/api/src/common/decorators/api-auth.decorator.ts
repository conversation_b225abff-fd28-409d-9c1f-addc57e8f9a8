import { UseGuards } from '@nestjs/common';
import { ApiTokenGuard } from '../guards/api-token.guard';

/**
 * Decorator to protect endpoints with API token authentication
 * 
 * Usage:
 * @ApiAuth()
 * @Get()
 * someProtectedEndpoint() { ... }
 * 
 * Clients must include the API token in one of these ways:
 * - Authorization: Bearer <token>
 * - Authorization: <token>
 * - X-API-Token: <token>
 */
export const ApiAuth = () => UseGuards(ApiTokenGuard);
